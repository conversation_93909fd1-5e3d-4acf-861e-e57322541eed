<template>
  <div class="app-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="app-breadcrumb" separator="/">
      <el-breadcrumb-item>
        <router-link to="/bizz/team">团队管理</router-link>
      </el-breadcrumb-item>
      <el-breadcrumb-item>团队详情</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 团队信息卡片 -->
    <el-card class="team-info-card" v-loading="teamLoading">
      <template #header>
        <div class="card-header">
          <span class="team-title">{{ teamInfo.teamName || '团队详情' }}</span>
          <el-button type="primary" icon="Back" @click="goBack">返回列表</el-button>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="团队名称">
          {{ teamInfo.teamName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="团队长">
          {{ teamInfo.leaderName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="团队长ID">
          {{ teamInfo.leaderId || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(teamInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}') || '-' }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 团队成员列表 -->
    <el-card class="member-list-card">
      <template #header>
        <div class="card-header">
          <span>团队成员</span>
          <span class="member-count">共 {{ total }} 人</span>
        </div>
      </template>

      <!-- 搜索栏 -->
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="姓名" prop="realName">
          <el-input
            v-model="queryParams.realName"
            placeholder="请输入成员姓名"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input
            v-model="queryParams.idCard"
            placeholder="请输入身份证号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 成员表格 -->
      <el-table v-loading="loading" :data="memberList">
        <el-table-column label="序号" type="index" width="50" align="center" />
        <el-table-column label="姓名" align="center" prop="realName" show-overflow-tooltip />
        <el-table-column label="用户类型" align="center" prop="userType">
          <template #default="scope">
            <dict-tag :options="sys_user_type" :value="scope.row.userType"/>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" align="center" prop="registerTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.registerTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getMemberList"
      />
    </el-card>
  </div>
</template>

<script setup>
import { getTeam, getTeamMembers } from "@/api/bizz/team";
import { parseTime } from "@/utils/ruoyi";

const { proxy } = getCurrentInstance();
const { sys_user_sex, sys_user_type } = proxy.useDict("sys_user_sex", "sys_user_type");
const route = useRoute();
const router = useRouter();

// 团队信息
const teamInfo = ref({});
const teamLoading = ref(true);

// 成员列表
const memberList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  teamId: route.params.teamId,
  realName: null,
  idCard: null
});

/** 获取团队信息 */
function getTeamInfo() {
  teamLoading.value = true;
  getTeam(route.params.teamId).then(response => {
    teamInfo.value = response.data;
    teamLoading.value = false;
  }).catch(() => {
    teamLoading.value = false;
  });
}

/** 获取成员列表 */
function getMemberList() {
  loading.value = true;
  // 如果后端API不存在，暂时使用模拟数据
  // getTeamMembers(route.params.teamId, queryParams).then(response => {
  //   memberList.value = response.rows;
  //   total.value = response.total;
  //   loading.value = false;
  // });
  
  // 模拟数据
  setTimeout(() => {
    const mockData = {
      rows: [
        {
          userId: '1',
          realName: '张三',
          idCard: '110101199001011234',
          phoneNumber: '13800138001',
          sex: '1',
          userType: 'general',
          registerTime: '2024-01-15 10:30:00'
        },
        {
          userId: '2', 
          realName: '李四',
          idCard: '110101199002021234',
          phoneNumber: '13800138002',
          sex: '0',
          userType: 'disciple',
          registerTime: '2024-01-20 14:20:00'
        }
      ],
      total: 2
    };
    
    memberList.value = mockData.rows;
    total.value = mockData.total;
    loading.value = false;
  }, 500);
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getMemberList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 返回列表 */
function goBack() {
  router.push('/bizz/team');
}

// 初始化数据
onMounted(() => {
  getTeamInfo();
  getMemberList();
});
</script>

<style scoped>
.app-breadcrumb {
  margin-bottom: 20px;
}

.team-info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.team-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.member-count {
  color: #909399;
  font-size: 14px;
}

.member-list-card {
  min-height: 400px;
}
</style>
