<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="用户姓名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="物料名称" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          placeholder="请输入物料名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="领取状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择领取状态" clearable>
          <el-option
            v-for="dict in material_receive_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户申请时间" prop="applyTime">
        <el-date-picker clearable
          v-model="queryParams.applyTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择用户点击确认申请的时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="管理发放时间" prop="confirmTime">
        <el-date-picker clearable
          v-model="queryParams.confirmTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="请选择管理员确认发放时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['bizz:material_receive_log:add']"
        >新增</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['bizz:material_receive_log:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="material_receive_logList">
      <el-table-column label="用户姓名" align="center" prop="userName" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="领取数量" align="center" prop="quantity" />
      <el-table-column label="领取状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="material_receive_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="用户申请时间" align="center" prop="applyTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.applyTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="管理发放时间" align="center" prop="confirmTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.confirmTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注信息" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Check"
            @click="handleConfirm(scope.row)"
            :disabled="scope.row.status !== 'applying'"
            v-hasPermi="['bizz:material_receive_log:confirm']"
          >确认</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加物料领取记录对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="material_receive_logRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input v-model="form.materialName" placeholder="请输入物料名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { listMaterial_receive_log, addMaterial_receive_log, confirmMaterial_receive_log } from "@/api/bizz/material_receive_log";

const { proxy } = getCurrentInstance();
const { material_receive_status } = proxy.useDict('material_receive_status');

const material_receive_logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: null,
    materialName: null,
    status: null,
    triggerType: null,
    applyTime: null,
    confirmTime: null,
  },
  rules: {
    userId: [
      { required: true, message: "领取用户ID不能为空", trigger: "blur" }
    ],
    materialId: [
      { required: true, message: "物料ID不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询物料领取记录列表 */
function getList() {
  loading.value = true;
  listMaterial_receive_log(queryParams.value).then(response => {
    material_receive_logList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    recordId: null,
    userId: null,
    userName: null,
    materialId: null,
    materialName: null,
    quantity: null,
    status: null,
    triggerType: null,
    applyTime: null,
    confirmTime: null,
    handlerId: null,
    handlerName: null,
    remark: null,
    createTime: null,
    updateTime: null
  };
  proxy.resetForm("material_receive_logRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}



/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加物料领取记录";
}

/** 确认按钮操作 */
function handleConfirm(row) {
  proxy.$modal.confirm('是否确认物料领取记录编号为"' + row.recordId + '"的数据项？').then(function() {
    return confirmMaterial_receive_log(row.recordId, null);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("确认成功");
  }).catch(() => {});
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["material_receive_logRef"].validate(valid => {
    if (valid) {
      addMaterial_receive_log(form.value).then(response => {
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        getList();
      });
    }
  });
}



/** 导出按钮操作 */
function handleExport() {
  proxy.download('bizz/material_receive_log/export', {
    ...queryParams.value
  }, `material_receive_log_${new Date().getTime()}.xlsx`)
}

getList();
</script>
