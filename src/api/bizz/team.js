import request from '@/utils/request'

// 查询团队管理列表
export function listTeam(query) {
  return request({
    url: '/bizz/team/list',
    method: 'get',
    params: query
  })
}

// 查询团队管理详细
export function getTeam(teamId) {
  return request({
    url: '/bizz/team/' + teamId,
    method: 'get'
  })
}

// 新增团队管理
export function addTeam(data) {
  return request({
    url: '/bizz/team',
    method: 'post',
    data: data
  })
}

// 修改团队管理
export function updateTeam(data) {
  return request({
    url: '/bizz/team',
    method: 'put',
    data: data
  })
}

// 删除团队管理
export function delTeam(teamId) {
  return request({
    url: '/bizz/team/' + teamId,
    method: 'delete'
  })
}

// 获取团队成员列表
export function getTeamMembers(teamId, query) {
  return request({
    url: `/bizz/team/${teamId}/members`,
    method: 'get',
    params: query
  })
}
